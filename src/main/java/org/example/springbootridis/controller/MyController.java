package org.example.springbootridis.controller;


import org.example.springbootridis.entity.Student;
import org.example.springbootridis.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RestController
public class MyController {
    @Autowired
    private RedisUtil redisUtil;
    @RequestMapping("/getOrderNo")
    public String index() {
        return redisUtil.generateOrderNo();
    }

    //存入字符串
    @RequestMapping("/setString")
    public void setString() {
        redisUtil.set("hh", "ads");
    }

    //存入对象
    @RequestMapping("/setObject")
    public void setObject() {
        Student student = new Student("沈辉", 18, "男", "<EMAIL>");
        redisUtil.set("student", student);
    }
    //获取对象
    @RequestMapping("/getObject")
    public Student getObject() {
        Student student = redisUtil.get("student", Student.class);
        return student;
    }
    //存入list
    @RequestMapping("/setList")
    public void setList() {
        Student student = new Student("沈辉", 18, "男", "<EMAIL>");
        Student student1 = new Student("张三", 18, "男", "<EMAIL>");
        Student student2 = new Student("李四", 18, "男", "<EMAIL>");
        redisUtil.setList("list", student);
        redisUtil.setList("list", student1);
        redisUtil.setList("list", student2);
    }
    //获取list
    @RequestMapping("/getList")
    public void getList() {
        List<Student> student = redisUtil.getList("list", Student.class);
        System.out.println(student);
    }

}
